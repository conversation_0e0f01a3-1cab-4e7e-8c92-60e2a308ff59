{"name": "RNProject", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "android:weee": "react-native run-android --mode officialDebug", "ios": "react-native run-ios", "lint": "eslint .", "dev": "npm run codepush:server & npm start", "start": "react-native start --reset-cache --host 0.0.0.0 --port 8081", "test": "jest", "enki-update": "git submodule update --init --recursive && cd modules/weee-ui && git pull origin rn-enki-stable", "generate-icons": "node scripts/generateIconMap.js", "// ========== RNModule 版本管理 ==========": "", "version:info": "node -e \"require('./src/version').showVersionInfo()\"", "version:bump:patch": "node -e \"require('./src/version').incrementVersion('patch')\"", "version:bump:minor": "node -e \"require('./src/version').incrementVersion('minor')\"", "version:bump:major": "node -e \"require('./src/version').incrementVersion('major')\"", "version:generate": "node -e \"require('./src/version').generateVersionFile('development')\"", "version:generate:prod": "node -e \"require('./src/version').generateVersionFile('production')\"", "version:check": "node src/version/index.js check", "// ========== Bundle 生成 ==========": "", "bundle:ios:debug": "node scripts/bundle-generator.js --platform ios --config debug --output ios/RNModule/main.jsbundle --assets-dir ios/RNModule/", "bundle:ios:release": "node scripts/bundle-generator.js --platform ios --config release --output ios/RNModule/main.jsbundle --assets-dir ios/RNModule/", "bundle:android:debug": "node scripts/bundle-generator.js --platform android --config debug --output android/app/src/main/assets/index.android.bundle", "bundle:android:release": "node scripts/bundle-generator.js --platform android --config release --output android/app/src/main/assets/index.android.bundle", "// ========== XCFramework 构建 ==========": "", "build:xcframework": "bash build_xcframework.sh", "build:ios": "bash build_ios.sh", "build:ios:debug": "bash build_ios.sh debug", "build:ios:release": "bash build_ios.sh release", "// ========== 完整工作流 ==========": "", "build:dev": "npm run version:generate && npm run bundle:ios:debug", "build:release": "npm run version:bump:patch && npm run bundle:ios:release", "// ========== CodePush 相关 ==========": "", "codepush:version": "node scripts/codepush-helper.js generate-version", "codepush:sign": "node scripts/codepush-helper.js sign-file", "codepush:release": "node scripts/codepush-helper.js create-release", "codepush:server": "node scripts/local-server.js", "codepush:test": "node scripts/test-codepush.js test", "// ========== Nitro 相关 ==========": "", "nitro:generate": "cd modules/weee-native && npm run nitro:generate", "nitro:build": "cd modules/weee-native && npm run nitro:build", "nitro:clean": "rm -rf modules/weee-native/nitrogen/generated && npm run nitro:generate", "nitro:ios": "npm run nitro:generate", "nitro:android": "cd modules/weee-native && npm run nitro:generate", "nitro:full": "npm run nitro:clean && npm run pods:install", "nitro:rebuild": "npm run nitro:clean && npm run pods:clean", "// ========== CocoaPods 相关 ==========": "", "pods:install": "cd ios && bundle exec pod install", "pods:update": "cd ios && bundle exec pod update", "pods:clean": "cd ios && rm -f Podfile.lock && bundle exec pod install", "pods:repo-update": "cd ios && bundle exec pod repo update", "// ========== 工具命令 ==========": "", "kill-port": "node scripts/kill-port.js", "kill:codepush": "node scripts/kill-port.js --codepush", "kill:metro": "node scripts/kill-port.js --metro"}, "dependencies": {"@bravemobile/react-native-code-push": "11.0.0", "@shopify/flash-list": "^2.0.3", "classnames": "^2.5.1", "i18next": "^25.3.6", "immer": "^10.1.3", "lottie-react-native": "^7.2.4", "moti": "^0.30.0", "nativewind": "^4.1.23", "react": "19.1.0", "react-content-loader": "^7.1.1", "react-i18next": "^15.6.1", "react-native": "0.80.0", "react-native-auto-skeleton": "^0.1.26", "react-native-crypto-js": "^1.0.0", "react-native-dotenv": "^3.4.11", "react-native-linear-gradient": "^2.8.3", "react-native-nitro-modules": "^0.28.1", "react-native-pager-view": "^6.8.1", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "^5.6.1", "react-native-tab-view": "^4.1.2", "react-native-worklets": "^0.5.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "weee-native": "file:./modules/weee-native", "zustand": "^5.0.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.1.0", "@react-native-community/cli-platform-android": "19.1.0", "@react-native-community/cli-platform-ios": "19.1.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@types/jest": "^29.5.13", "@types/node": "^24.0.3", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "glob": "^11.0.3", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "ts-node": "^10.9.2", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "codegenConfig": {"name": "WeeeRouteModuleSpec", "type": "all", "jsSrcsDir": "specs", "android": {"javaPackageName": "com.sayweee.react.spec"}}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}