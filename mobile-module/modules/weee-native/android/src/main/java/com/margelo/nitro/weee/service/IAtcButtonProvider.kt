package com.margelo.nitro.weee.service

import android.view.View
import com.facebook.react.uimanager.ThemedReactContext
import com.margelo.nitro.weee.data.AtcProductData

/**
 * Interface for ATC UI component operations
 * This abstracts all UI operations to avoid direct dependencies on main app UI components
 */
interface IAtcButtonProvider {

    /**
     * Get the root view of the ATC component
     */
    val rootView: View

    /**
     * Initialize the UI component with context
     */
    fun initialize(context: ThemedReactContext?)

    /**
     * Initialize with product data and set up OpHelper
     */
    fun initializeWithProductData(product: AtcProductData)

    /**
     * Set click listener for operations
     */
    fun setOperationListener(listener: AtcOperationListener)

    /**
     * Interface for handling user operations on the ATC component
     */
    interface AtcOperationListener {

        fun onUpdate()

        /**
         * Get the product ID for this component
         */
        fun getProductId(): Int
    }
}
