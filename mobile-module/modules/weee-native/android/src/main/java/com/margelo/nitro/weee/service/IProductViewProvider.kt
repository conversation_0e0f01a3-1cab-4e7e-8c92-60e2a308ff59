package com.margelo.nitro.weee.service

import android.view.View
import com.facebook.react.uimanager.ThemedReactContext

/**
 * Interface for providing ProductView from main app to Nitro module
 * This allows the main app to inject its own ProductView component
 */
interface IProductViewProvider {
    /**
     * Create a ProductView instance from main app
     */
    fun createProductView(context: ThemedReactContext): View

    /**
     * Set product JSON data to the ProductView
     * This method should handle the JSON parsing and ProductBean conversion
     */
    fun setProductJson(productView: View, productJson: String)

}

/**
 * Event listener interface for CartOpLayout events
 */
interface CartOpEventListener {
    /**
     * Called when user clicks the left button (decrease quantity)
     */
    fun onDecreaseClicked(productId: Int, currentQuantity: Int, newQuantity: Int)

    /**
     * Called when user clicks the right button (increase quantity)
     */
    fun onIncreaseClicked(productId: Int, currentQuantity: Int, newQuantity: Int)

    /**
     * Called when user clicks on the quantity number
     */
    fun onQuantityClicked(productId: Int, currentQuantity: Int)

    /**
     * Called when cart operation is completed
     */
    fun onCartOperationCompleted(productId: Int, finalQuantity: Int, operation: String)
}
