package com.margelo.nitro.weee.service

import com.facebook.react.uimanager.ThemedReactContext

/**
 * Service provider for ATC component dependencies
 * This uses dependency injection pattern to avoid direct coupling with main app classes
 */
object UIProvideManager {

    private var uiComponentFactory: IUIComponentFactory? = null
    private var productViewProvider: IProductViewProvider? = null


    /**
     * Register UI component factory implementation from main app
     */
    fun registerUIComponentFactory(factory: IUIComponentFactory) {
        this.uiComponentFactory = factory
    }

    /**
     * Register ProductView provider implementation from main app
     */
    fun registerProductViewProvider(provider: IProductViewProvider) {
        this.productViewProvider = provider
    }

    /**
     * Create UI component instance
     */
    fun createAtcButton(context: ThemedReactContext?): IAtcButtonProvider {
        val factory = uiComponentFactory ?: throw IllegalStateException(
            "UI component factory not registered. Call registerUIComponentFactory() from main app first."
        )
        return factory.createAtcButton(context)
    }

    /**
     * Get ProductView provider implementation
     */
    fun getProductViewProvider(): IProductViewProvider {
        return productViewProvider ?: throw IllegalStateException(
            "ProductView provider not registered. Call registerProductViewProvider() from main app first."
        )
    }

    /**
     * Check if ProductView provider is registered
     */
    fun isProductViewProviderInitialized(): Boolean {
        return productViewProvider != null
    }

    /**
     * Clear all registrations (for testing)
     */
    fun clear() {
        uiComponentFactory = null
        productViewProvider = null
    }
}

/**
 * Factory interface for creating UI components
 */
interface IUIComponentFactory {
    /**
     * Create a new UI component instance
     */
    fun createAtcButton(context: ThemedReactContext?): IAtcButtonProvider
}
