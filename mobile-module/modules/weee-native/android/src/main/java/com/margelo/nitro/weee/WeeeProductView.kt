package com.margelo.nitro.weee

import android.util.Log
import android.view.View
import com.facebook.react.uimanager.ThemedReactContext
import com.margelo.nitro.weee.service.UIProvideManager

///**
// * Data class for CartOpLayout events
// */
//data class CartOpEvent(
//    var type: String = "",
//    var productId: Int = 0,
//    var currentQuantity: Int = 0,
//    var newQuantity: Int = 0,
//    var operation: String = ""
//)

/**
 * WeeeProductView - Nitro module wrapper for main app's ProductView
 * Uses dependency injection to get ProductView from main app:
 * 1. Gets ProductView provider from UIProvideManager
 * 2. Creates ProductView instance through provider
 * 3. Sets productJson through provider's setProductJson method
 */
class WeeeProductView(private var reactContext: ThemedReactContext?) : HybridWeeeProductViewSpec() {

    companion object {
        private const val TAG = "WeeeProductView"
    }

    private var productView: View? = null
    private var _productJson: String? = null
//    private var cartEventCallback: ((CartOpEvent) -> Unit)? = null
    private var _opUpdate: Double? = null

    init {
        initializeProductView()
    }

    override var productJson: String?
        get() = _productJson
        set(value) {
            _productJson = value
            setProductJsonToView(value)
        }
//    override var onUpdate: () -> Unit
//        get() = TODO("Not yet implemented")
//        set(value) {}
//    override var onCartOpEvent: ((CartOpEvent) -> Unit)?
//        get() = cartEventCallback
//        set(value) { cartEventCallback = value}

//    override var opUpdate: Double?
//        get() = _opUpdate
//        set(value) {
//            Log.d(TAG, "Setting opUpdate to: $value")
//            _opUpdate = value
//            updateCartOpLayoutOpacity(value)
//        }

//    override fun setOpUpdate(value: Double) {
//        Log.d(TAG, "Setting setOpUpdate to: $value")
//        updateCartOpLayoutOpacity(value)
//    }

    /**
     * Set callback for cart operation events
     */
//    fun setOnCartOpEventListener(callback: (CartOpEvent) -> Unit) {
//        cartEventCallback = callback
//        Log.d(TAG, "Cart operation event callback set")
//    }

    override val view: View
        get() = productView ?: createEmptyView()



    /**
     * 通过依赖注入的方式更新 CartOpLayout 的 opacity
     * 委托给 ProductViewProvider 来实现具体的 opacity 更新逻辑
     */
//    private fun updateCartOpLayoutOpacity(opUpdate: Double?) {
//        try {
//            val productView = this.productView
//            if (productView != null) {
//                // 通过 UIProvideManager 获取 ProductViewProvider 实例
//                val provider = com.margelo.nitro.weee.service.UIProvideManager.getProductViewProvider()
//                if (provider != null) {
//                    Log.d(TAG, "Delegating opacity update to ProductViewProvider, opUpdate: $opUpdate")
//                    provider.updateCartOpLayoutOpacity(productView, opUpdate)
//                } else {
//                    Log.w(TAG, "ProductViewProvider is null, cannot update opacity")
//                }
//            } else {
//                Log.w(TAG, "ProductView is null, cannot update opacity")
//            }
//        } catch (e: Exception) {
//            Log.e(TAG, "Error updating CartOpLayout opacity", e)
//        }
//    }

    /**
     * Initialize the ProductView component through dependency injection
     */
    private fun initializeProductView() {
        try {
            if (reactContext != null) {
                // Check if ProductView provider is registered
                if (!UIProvideManager.isProductViewProviderInitialized()) {
                    throw IllegalStateException("ProductView provider not registered. Please register ProductViewProvider in main app first.")
                }

                // Get ProductView provider from main app through dependency injection
                val provider = UIProvideManager.getProductViewProvider()

                // Create ProductView instance through provider
                productView = provider.createProductView(reactContext!!)

//                // Set up cart operation event listener
//                setupCartOpEventListener(provider)

//                Log.d(TAG, "ProductView initialized successfully through dependency injection")
            } else {
                Log.e(TAG, "ReactContext is null, cannot initialize ProductView")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing ProductView through dependency injection", e)
        }
    }

    /**
     * Set product JSON to ProductView through provider
     */
    private fun setProductJsonToView(json: String?) {
        if (json.isNullOrEmpty()) {
            Log.w(TAG, "Product JSON is null or empty")
            return
        }

        try {
            val productView = this.productView
            if (productView != null) {
                // Get provider and set product JSON
                val provider = UIProvideManager.getProductViewProvider()
                provider.setProductJson(productView, json)

//                Log.d(TAG, "Product JSON set successfully through provider")

                // Set up cart operation event listener
//                setupCartOpEventListener(provider)

            } else {
                Log.e(TAG, "ProductView is null, cannot set product JSON")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting product JSON through provider", e)
        }
    }

    /**
     * Set up cart operation event listener
     */
//    private fun setupCartOpEventListener(provider: com.margelo.nitro.weee.service.IProductViewProvider) {
//        val productView = this.productView ?: return
//
//        val cartEventListener = object : CartOpEventListener {
//            override fun onDecreaseClicked(productId: Int, currentQuantity: Int, newQuantity: Int) {
//                Log.d(TAG, "Decrease clicked: productId=$productId, current=$currentQuantity, new=$newQuantity")
//                triggerCartEvent("decrease_clicked", productId, currentQuantity, newQuantity, "decrease")
//            }
//
//            override fun onIncreaseClicked(productId: Int, currentQuantity: Int, newQuantity: Int) {
//                Log.d(TAG, "Increase clicked: productId=$productId, current=$currentQuantity, new=$newQuantity")
//                triggerCartEvent("increase_clicked", productId, currentQuantity, newQuantity, "increase")
//            }
//
//            override fun onQuantityClicked(productId: Int, currentQuantity: Int) {
//                Log.d(TAG, "Quantity clicked: productId=$productId, quantity=$currentQuantity")
//                triggerCartEvent("quantity_clicked", productId, currentQuantity, currentQuantity, "click")
//            }
//
//            override fun onCartOperationCompleted(productId: Int, finalQuantity: Int, operation: String) {
//                Log.d(TAG, "Cart operation completed: productId=$productId, finalQuantity=$finalQuantity, operation=$operation")
////                triggerCartEvent("operation_completed", productId, finalQuantity, finalQuantity, operation)
//            }
//        }
//
//        provider.setCartOpEventListener(productView, cartEventListener)
//        Log.d(TAG, "Cart operation event listener set up successfully")
//    }

    /**
     * Trigger cart operation event to callback
     * 简化实现，移除双重动画，依赖 RN 端的 opUpdate 属性控制视觉反馈
     */
//    private fun triggerCartEvent(type: String, productId: Int, currentQuantity: Int, newQuantity: Int, operation: String) {
//        try {
//            val event = CartOpEvent(
//                type = type,
//                productId = productId.toDouble(),
//                currentQuantity = currentQuantity.toDouble(),
//                newQuantity = newQuantity.toDouble(),
//                operation = operation
//            )
//
//            cartEventCallback?.invoke(event)
//            Log.d(TAG, "Cart event triggered: $type, productId: $productId, operation: $operation")
//            Log.d(TAG, "Visual feedback will be controlled by RN opUpdate property")
//        } catch (e: Exception) {
//            Log.e(TAG, "Error triggering cart event", e)
//        }
//    }



    /**
     * Create empty view as fallback
     */
    private fun createEmptyView(): View {
        return View(reactContext).apply {
            Log.w(TAG, "Returning empty view as fallback")
        }
    }
}