
import WeeeAtcButtonConfig from '../../nitrogen/generated/shared/json/WeeeAtcButtonConfig.json';

import {getHostComponent} from 'react-native-nitro-modules';

import {ViewStyle, View} from 'react-native';

import React, {forwardRef, useState} from 'react';


import type {
  AtcButtonProps,
  AtcButtonMethods,
} from '../nitro/WeeeAtcButton.nitro';

// Create the hybrid view components using getHostComponent
const WeeeAtcButtonNative = getHostComponent<AtcButtonProps, AtcButtonMethods>(
  'WeeeAtcButton',
  () => WeeeAtcButtonConfig,
);


// Define props interface for the wrapped component
export interface WeeeAtcButtonProps extends Omit<AtcButtonProps, 'style'> {
  className?: string;
  style?: ViewStyle;
}

// Wrapped component that supports NativeWind className
 const WeeeAtcButton = forwardRef<any, WeeeAtcButtonProps>(
  ({className, style, ...restProps}, ref) => {
    // 从 restProps 中排除 onListener，因为我们要自己处理
    const { onUpdate, ...props } = restProps;

    const [refreshStatus, setRefreshStatus] = useState(false);

    // 创建事件监听器
    const eventListener = () => {
      try {
        setRefreshStatus(true);
        setTimeout(() => setRefreshStatus(false), 300);
      } catch (error) {
        console.error('WeeeAtcButton event handling error:', error);
      }
    };

    if (className) {
      return (
        <View className={className} style={[style, refreshStatus && {backgroundColor: "#ffffff"}]}>
          <WeeeAtcButtonNative
            style={{flex: 1, ...style}}
            onUpdate={{ f: eventListener }}
            {...props}
          />
        </View>
      );
    }
    return (
      <WeeeAtcButtonNative
        className={className}
        style={[style, refreshStatus && {opacity: 0.99}]}
        onUpdate={{ f: eventListener }}
        {...props}
      />
    );
  },
);

WeeeAtcButton.displayName = 'WeeeAtcButton';

export default WeeeAtcButton;