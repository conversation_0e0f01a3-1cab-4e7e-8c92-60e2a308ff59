import WeeeProductViewConfig from '../../nitrogen/generated/shared/json/WeeeProductViewConfig.json';

import {getHostComponent} from 'react-native-nitro-modules';

import {ViewStyle, View} from 'react-native';

import React, {forwardRef, useRef, useState} from 'react';


import WeeeAtcButton from './WeeeAtcButton';

import type {
  ProductViewProps,
  ProductViewMethods,
} from '../nitro/WeeeProductView.nitro';

const WeeeProductViewNative = getHostComponent<ProductViewProps, ProductViewMethods>(
  'WeeeProductView',
  () => WeeeProductViewConfig,
);


interface WeeeProductViewProps extends ProductViewProps {
  className?: string;
  style?: ViewStyle;
  productJson?: string;
}

// WeeeProductView component - wraps main app's ProductView
const WeeeProductView = forwardRef<View, WeeeProductViewProps>(
  ({ className, style, ...props }, ref) => {


    const [refreshStatus, setRefreshStatus] = useState(false);

    if (className) {
      return (
        <View className={className} style={[style]}>

          <WeeeProductViewNative
            style={style}
            {...props}
          />

          <View style={{position: 'absolute', bottom: 20, right: 20, }}>
            <WeeeAtcButton
              onUpdate={() => {
              }}
              className="h-[40px] w-[100px]"
              productJson={props.productJson}  />
          </View>
        </View>
      );
    }
    return (
      <View style={[style]}>
        <WeeeProductViewNative
          style={style}
          {...props}
        />
        <View style={{position: 'absolute', bottom: 20, right: 20, }}>
          <WeeeAtcButton
            onUpdate={() => {
            }}
            className="h-[40px] w-[100px]"
            productJson={props.productJson}  />
        </View>
      </View>
    );
  },
);

WeeeProductView.displayName = 'WeeeProductView';

export default WeeeProductView;