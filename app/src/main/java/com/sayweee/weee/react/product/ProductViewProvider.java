package com.sayweee.weee.react.product;

import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import com.facebook.react.uimanager.ThemedReactContext;
import com.margelo.nitro.weee.service.IProductViewProvider;
import com.margelo.nitro.weee.service.CartOpEventListener;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.weee.widget.op.CartOpLayout;

import java.util.HashMap;
import java.util.Map;

/**
 * ProductView provider implementation for Nitro module
 * This class bridges the main app's ProductView with the Nitro module
 */
public class ProductViewProvider implements IProductViewProvider {

    private static final String TAG = "ProductViewProvider";

    @Override
    public View createProductView(ThemedReactContext context) {
        try {
            // Create ProductView instance from main app
            ProductView productView = new ProductView(context, ProductView.STYLE_LIST);
            Log.d(TAG, TAG + " created successfully");
            return productView;
        } catch (Exception e) {
            Log.e(TAG, "Error creating ProductView", e);
            // Return empty view as fallback
            return new View(context);
        }
    }

    @Override
    public void setProductJson(View productView, String productJson) {
        if (!(productView instanceof ProductView)) {
            Log.e(TAG, "View is not a ProductView instance");
            return;
        }

        if (productJson == null || productJson.isEmpty()) {
            Log.w(TAG, "Product JSON is null or empty");
            return;
        }

        try {
            // Parse JSON to ProductBean
            ProductBean productBean = JsonUtils.parseObject(productJson, ProductBean.class);

            if (productBean == null) {
                Log.e(TAG, "Failed to parse ProductBean from JSON");
                return;
            }

            // Set product to ProductView using main app's standard method
            ProductView view = (ProductView) productView;

            // Use the same parameters as main app
            String source = "nitro_product_view";
            Map<String, Object> element = new HashMap<>();
            Map<String, Object> ctx = new HashMap<>();

            // Set product using ProductView's standard method with OnOpCallback
            // This OnOpCallback is CRITICAL for triggering OpHelper.helperOp() and animations
//            view.setAttachedProduct(productBean, ProductView.STYLE_LIST, new ProductView.OnOpCallback() {
//                @Override
//                public void onOp(com.sayweee.weee.widget.op.CartOpLayout layoutOp, ProductBean bean) {
//                    // This is the key missing piece that makes animations work!
//                    // OpHelper.helperOp() sets up the CartOpLayout properly with animations
//                    Log.d(TAG, "OnOpCallback triggered for product: " + bean.getProductId());
//
//                    try {
//                        // Call OpHelper.helperOp to set up CartOpLayout with animations and business logic
//                        // Use the correct method signature: helperOp(layoutOp, bean, target, source, element, ctx)
//                        com.sayweee.weee.module.cate.product.adapter.OpHelper.helperOp(
//                            layoutOp,
//                            bean,
//                            bean,  // Use bean as target (AdapterDataRefresh)
//                            source,
//                            element,
//                            ctx
//                        );
//
//                        Log.d(TAG, "OpHelper.helperOp called successfully for ProductView with correct signature");
//                    } catch (Exception e) {
//                        Log.e(TAG, "Error calling OpHelper.helperOp in ProductView", e);
//                    }
//                }
//            });
            view.setAttachedProduct(productBean, ProductView.STYLE_LIST, "", null, null);

            // Store ProductBean in view's tag for event listener access
            view.setTag(com.sayweee.weee.R.id.tag_product_bean, productBean);


            ((ProductView) productView).getCartOpLayout().setVisibility(View.GONE);

            Log.d(TAG, TAG + " setProductJson: " + productBean.name);

        } catch (Exception e) {
            Log.e(TAG, "Error setting product JSON to ProductView", e);
        }
    }

//    @Override
//    public void setCartOpEventListener(View productView, CartOpEventListener listener) {
//        if (!(productView instanceof ProductView)) {
//            Log.e(TAG, "View is not a ProductView instance");
//            return;
//        }
//
//        try {
//            ProductView view = (ProductView) productView;
//
//            // Store the listener in the view's tag for later access
////            view.setTag(com.sayweee.weee.R.id.tag_cart_event_listener, listener);
//
//            // Get CartOpLayout from ProductView
//            CartOpLayout cartOpLayout = getCartOpLayoutFromProductView(view);
//
//            if (cartOpLayout != null && listener != null) {
//                // IMPORTANT: Add delay to ensure OpHelper.helperOp() has completed its setup
//                // OpHelper.helperOp() is called in the OnOpCallback and sets up the CartOpLayout
//                cartOpLayout.post(() -> {
//                    try {
//                        Log.d(TAG, "Setting up CartOpLayout listener after OpHelper.helperOp() completion");
//
//                        // Check if we should use wrapper approach or direct approach
//                        if (shouldUseListenerWrapper(cartOpLayout)) {
//                            setupWrappedCartOpLayoutListener(cartOpLayout, listener, view);
//                        } else {
//                            setupDirectCartOpLayoutListener(cartOpLayout, listener, view);
//                        }
//                        Log.d(TAG, "CartOpLayout event listener set successfully");
//                    } catch (Exception e) {
//                        Log.e(TAG, "Error in delayed CartOpLayout listener setup", e);
//                    }
//                });
//            } else {
//                Log.w(TAG, "CartOpLayout not found or listener is null");
//            }
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error setting CartOpLayout event listener", e);
//        }
//    }

    /**
     * Get CartOpLayout from ProductView
     */
    private CartOpLayout getCartOpLayoutFromProductView(ProductView productView) {
        try {
            // ProductView should have a method to get CartOpLayout
            // This might need to be adjusted based on the actual ProductView implementation
            return productView.getCartOpLayout();
        } catch (Exception e) {
            Log.w(TAG, "Could not get CartOpLayout from ProductView", e);
            return null;
        }
    }

    /**
     * Check if we should use listener wrapper approach
     */
    private boolean shouldUseListenerWrapper(CartOpLayout cartOpLayout) {
        try {
            // Check if there's already a listener set
            CartOpLayout.OnCartOpListener existingListener = getOriginalListener(cartOpLayout);
            boolean hasExistingListener = existingListener != null;

            Log.d(TAG, "shouldUseListenerWrapper: hasExistingListener=" + hasExistingListener);
            return hasExistingListener;
        } catch (Exception e) {
            Log.w(TAG, "Error checking for existing listener, using wrapper approach", e);
            return true; // Default to wrapper approach for safety
        }
    }

    /**
     * Set up wrapped CartOpLayout listener that preserves existing functionality
     */
    private void setupWrappedCartOpLayoutListener(CartOpLayout cartOpLayout, CartOpEventListener listener, ProductView productView) {
        try {
            // Store the original listener if it exists
            CartOpLayout.OnCartOpListener originalListener = getOriginalListener(cartOpLayout);

            // Create a wrapper listener that calls both original and new listener
            CartOpLayout.OnCartOpListener wrappedListener = new CartOpLayout.OnCartOpListener() {
                @Override
                public void operateLeft(View view) {
                    Log.d(TAG, "Wrapped operateLeft called - executing original + custom logic");

                    // First call the original listener if it exists
                    if (originalListener != null) {
                        try {
                            originalListener.operateLeft(view);
                        } catch (Exception e) {
                            Log.w(TAG, "Error calling original operateLeft listener", e);
                        }
                    }

                    // Then call our custom listener
                    ProductBean bean = getProductBeanFromView(productView);
                    if (bean != null && listener != null) {
                        int currentQuantity = cartOpLayout.getTextNum();
                        int newQuantity = Math.max(0, currentQuantity - 1);

                        Log.d(TAG, "Custom decrease clicked: productId=" + bean.getProductId() +
                              ", current=" + currentQuantity + ", new=" + newQuantity);

                        listener.onDecreaseClicked(bean.getProductId(), currentQuantity, newQuantity);
                        listener.onCartOperationCompleted(bean.getProductId(), newQuantity, "decrease");
                    }
                }

                @Override
                public void operateRight(View view) {
                    Log.d(TAG, "Wrapped operateRight called - executing original + custom logic");

                    // First call the original listener if it exists
                    if (originalListener != null) {
                        try {
                            originalListener.operateRight(view);
                        } catch (Exception e) {
                            Log.w(TAG, "Error calling original operateRight listener", e);
                        }
                    }

                    // Then call our custom listener
                    ProductBean bean = getProductBeanFromView(productView);
                    if (bean != null && listener != null) {
                        int currentQuantity = cartOpLayout.getTextNum();
                        int newQuantity = currentQuantity + 1;

                        Log.d(TAG, "Custom increase clicked: productId=" + bean.getProductId() +
                              ", current=" + currentQuantity + ", new=" + newQuantity);

                        listener.onIncreaseClicked(bean.getProductId(), currentQuantity, newQuantity);
                        listener.onCartOperationCompleted(bean.getProductId(), newQuantity, "increase");
                    }
                }

                @Override
                public void onNumClick(View view) {
                    Log.d(TAG, "Wrapped onNumClick called - executing original + custom logic");

                    // First call the original listener if it exists
                    if (originalListener != null) {
                        try {
                            originalListener.onNumClick(view);
                        } catch (Exception e) {
                            Log.w(TAG, "Error calling original onNumClick listener", e);
                        }
                    }

                    // Then call our custom listener
                    ProductBean bean = getProductBeanFromView(productView);
                    if (bean != null && listener != null) {
                        int currentQuantity = cartOpLayout.getTextNum();

                        Log.d(TAG, "Custom quantity clicked: productId=" + bean.getProductId() +
                              ", quantity=" + currentQuantity);

                        listener.onQuantityClicked(bean.getProductId(), currentQuantity);
                    }
                }

                @Override
                public int getProductId() {
                    // Try to get from original listener first
                    if (originalListener != null) {
                        try {
                            return originalListener.getProductId();
                        } catch (Exception e) {
                            Log.w(TAG, "Error getting productId from original listener", e);
                        }
                    }

                    // Fallback to ProductBean
                    ProductBean bean = getProductBeanFromView(productView);
                    return bean != null ? bean.getProductId() : 0;
                }
            };

            // Set the wrapped listener
            cartOpLayout.setOnOperateListener(wrappedListener);

            Log.d(TAG, "Wrapped CartOpLayout listener set successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error setting up wrapped CartOpLayout listener", e);
        }
    }

    /**
     * Set up direct CartOpLayout listener when no existing listener is present
     */
    private void setupDirectCartOpLayoutListener(CartOpLayout cartOpLayout, CartOpEventListener listener, ProductView productView) {
        try {
            Log.d(TAG, "Setting up direct CartOpLayout listener (no existing listener found)");

            cartOpLayout.setOnOperateListener(new CartOpLayout.OnCartOpListener() {
                @Override
                public void operateLeft(View view) {
                    ProductBean bean = getProductBeanFromView(productView);
                    if (bean != null && listener != null) {
                        int currentQuantity = cartOpLayout.getTextNum();
                        int newQuantity = Math.max(0, currentQuantity - 1);

                        Log.d(TAG, "Direct decrease clicked: productId=" + bean.getProductId() +
                              ", current=" + currentQuantity + ", new=" + newQuantity);

                        listener.onDecreaseClicked(bean.getProductId(), currentQuantity, newQuantity);
                        listener.onCartOperationCompleted(bean.getProductId(), newQuantity, "decrease");
                    }
                }

                @Override
                public void operateRight(View view) {
                    ProductBean bean = getProductBeanFromView(productView);
                    if (bean != null && listener != null) {
                        int currentQuantity = cartOpLayout.getTextNum();
                        int newQuantity = currentQuantity + 1;

                        Log.d(TAG, "Direct increase clicked: productId=" + bean.getProductId() +
                              ", current=" + currentQuantity + ", new=" + newQuantity);

                        listener.onIncreaseClicked(bean.getProductId(), currentQuantity, newQuantity);
                        listener.onCartOperationCompleted(bean.getProductId(), newQuantity, "increase");
                    }
                }

                @Override
                public void onNumClick(View view) {
                    ProductBean bean = getProductBeanFromView(productView);
                    if (bean != null && listener != null) {
                        int currentQuantity = cartOpLayout.getTextNum();

                        Log.d(TAG, "Direct quantity clicked: productId=" + bean.getProductId() +
                              ", quantity=" + currentQuantity);

                        listener.onQuantityClicked(bean.getProductId(), currentQuantity);
                    }
                }

                @Override
                public int getProductId() {
                    ProductBean bean = getProductBeanFromView(productView);
                    return bean != null ? bean.getProductId() : 0;
                }
            });

            Log.d(TAG, "Direct CartOpLayout listener set successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error setting up direct CartOpLayout listener", e);
        }
    }

    /**
     * Get the original listener from CartOpLayout using reflection
     * This preserves the existing functionality while adding our custom listener
     */
    private CartOpLayout.OnCartOpListener getOriginalListener(CartOpLayout cartOpLayout) {
        try {
            // Use reflection to get the current listener
            java.lang.reflect.Field listenerField = CartOpLayout.class.getDeclaredField("listener");
            listenerField.setAccessible(true);
            Object listener = listenerField.get(cartOpLayout);

            if (listener instanceof CartOpLayout.OnCartOpListener) {
                Log.d(TAG, "Found existing CartOpLayout listener: " + listener.getClass().getSimpleName());
                return (CartOpLayout.OnCartOpListener) listener;
            } else {
                Log.d(TAG, "No existing CartOpLayout listener found");
                return null;
            }
        } catch (Exception e) {
            Log.w(TAG, "Could not get original CartOpLayout listener via reflection", e);
            return null;
        }
    }

    /**
     * Get ProductBean from ProductView's tag
     */
    private ProductBean getProductBeanFromView(ProductView productView) {
        try {
            Object tag = productView.getTag(com.sayweee.weee.R.id.tag_product_bean);
            if (tag instanceof ProductBean) {
                return (ProductBean) tag;
            }
        } catch (Exception e) {
            Log.w(TAG, "Could not get ProductBean from view tag", e);
        }
        return null;
    }

//    @Override
//    public void updateCartOpLayoutOpacity(View productView, Double opUpdate) {
//        if (!(productView instanceof ProductView)) {
//            Log.e(TAG, "View is not a ProductView instance");
//            return;
//        }
//
//        try {
//            ProductView pv = (ProductView) productView;
//            CartOpLayout cartOpLayout = getCartOpLayoutFromProductView(pv);
//
//            if (cartOpLayout != null && opUpdate != null) {
//                // 将 opUpdate 值转换为 opacity (0.0 - 1.0)
//                float opacity = calculateOpacity(opUpdate);
//
//                Log.d(TAG, "Updating CartOpLayout opacity to: " + opacity + " (from opUpdate: " + opUpdate + ")");
//
//
//            } else {
//                if (cartOpLayout == null) {
//                    Log.w(TAG, "CartOpLayout is null, cannot update opacity");
//                } else {
//                    Log.d(TAG, "opUpdate is null, skipping opacity update");
//                }
//            }
//        } catch (Exception e) {
//            Log.e(TAG, "Error updating CartOpLayout opacity", e);
//        }
//    }

    /**
     * Calculate opacity value from opUpdate
     * 将 opUpdate 数值转换为合适的 opacity 值
     */
    private float calculateOpacity(Double opUpdate) {
        if (opUpdate == null) {
            return 1.0f;
        }

        // 使用 opUpdate 的变化来触发视觉反馈
        // 不管 opUpdate 的具体值，都设置为轻微透明来触发重渲染
        return 0.99f;
    }
}
