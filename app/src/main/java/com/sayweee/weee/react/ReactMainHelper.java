package com.sayweee.weee.react;

import com.margelo.nitro.weee.service.UIProvideManager;
import com.sayweee.weee.react.atc.UIComponentFactory;
import com.sayweee.weee.react.product.ProductViewProvider;


import android.app.Application;
import android.content.Context;

import androidx.annotation.Nullable;

import com.facebook.react.PackageList;
import com.facebook.react.ReactPackage;
import com.facebook.react.common.assets.ReactFontManager;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactNativeHost;
import com.facebook.react.soloader.OpenSourceMergedSoMapping;
import com.facebook.soloader.SoLoader;
import com.margelo.nitro.weee.ViewFactoryManager;
import com.margelo.nitro.weee.WeeeNativeOnLoad;
import com.microsoft.codepush.react.CodePush;
import com.sayweee.react.sync.ReactSyncManager;
import com.sayweee.weee.BuildConfig;
import com.sayweee.weee.R;
import com.sayweee.weee.react.component.ReactComponentPackage;

import java.io.IOException;
import java.util.List;

//
// Created by Thomsen on 18/06/2025.
//
public class ReactMainHelper {

    public static DefaultReactNativeHost reactNativeHost(Application context) {
        return new DefaultReactNativeHost(context) {
            @Override
            public boolean getUseDeveloperSupport() {
                return BuildConfig.DEBUG;
            }

            @Override
            protected List<ReactPackage> getPackages() {
                List<ReactPackage> packages = new PackageList(context).getPackages();
                packages.add(new ReactComponentPackage());
                return packages;
            }

            @Override
            protected String getJSMainModuleName() {
                return "index";
            }

            @Override
            protected boolean isNewArchEnabled() {
                return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
            }

            @Nullable
            @Override
            protected Boolean isHermesEnabled() {
                return BuildConfig.IS_HERMES_ENABLED;
            }

            @Override
            protected String getJSBundleFile() {
//                return super.getJSBundleFile();
                return CodePush.getJSBundleFile();
            }
        };
    }

    public static void loadReactNative(Context context) {
        ReactFontManager.getInstance().addCustomFont(context, "poppins", R.font.poppins);
        ReactFontManager.getInstance().addCustomFont(context, "be_vietnam_pro", R.font.be_vietnam_pro);
        try {
            SoLoader.init(context, OpenSourceMergedSoMapping.INSTANCE);
        } catch (IOException error) {
            throw new RuntimeException(error);
        }

        if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
            DefaultNewArchitectureEntryPoint.load();
        }

        ReactSyncManager.INSTANCE.initialize(new ReactSyncImpl());

        WeeeNativeOnLoad.initializeNative();

        ViewFactoryManager.Companion.getINSTANCE().registerFactory(new AppCartLayoutFactory());

        // Register AtcServiceProvider dependencies for nitro ATC component
        UIProvideManager.INSTANCE.registerUIComponentFactory(new UIComponentFactory());
        UIProvideManager.INSTANCE.registerProductViewProvider(new ProductViewProvider());

    }
}
