package com.sayweee.weee.react.atc;

import com.facebook.react.uimanager.ThemedReactContext;
import com.margelo.nitro.weee.service.IAtcButtonProvider;
import com.margelo.nitro.weee.service.IUIComponentFactory;

/**
 * Factory implementation for creating AtcUIComponent instances in main app
 * This factory creates CartOpLayoutUIComponent instances that wrap the existing CartOpLayout
 */
public class UIComponentFactory implements IUIComponentFactory {

    @Override
    public IAtcButtonProvider createAtcButton(ThemedReactContext context) {
        AtcButtonProvider component = new AtcButtonProvider();
        component.initialize(context);
        return component;
    }
}
